/*
https://docs.nestjs.com/providers#services
*/

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ACTIVITY_TYPE, ChatActivityDto, ChatUserDto } from 'src/dto/dtos';
import { LogService } from 'src/log/log.service';
import { ChatUser } from 'src/models/chat-user.model';
import { Chat } from 'src/models/chat.model';
import { ChatActivity } from 'src/models/chat_activity_response.model';

@Injectable()
export class ActivityService {

  constructor(@InjectModel(ChatActivity.name) private readonly chatActivityModel: Model<ChatActivity>, private logService: LogService) { }
  async search(message: string, date: string, user: ChatUserDto) {
    const query = { tenantId: user.tenantId }

    if (message) {
      query['description'] = { $regex: message, $options: 'i' };
    }
    if (date) {
      query['createdAt'] = { $regex: date, $options: 'i' };
    }
    const activities = await this.chatActivityModel.find(query);
    return activities;
  }
  async updateReadStatus(id: string) {
    return await this.chatActivityModel.updateOne({ _id: id }, { isRead: true });
  }
  async createActivity(activity: ChatActivityDto) {
    const saved = await this.chatActivityModel.create(activity);
    await saved.save();
    return await this.findById(saved.id)
  }
  async findById(id: string) {
    return await this.chatActivityModel.findById(id).populate({
      path: 'message',
      model: 'ChatMessage'
    }).populate({
      path: 'sender',
      model: 'ChatUser',
      select: '-chats'
    }).populate({
      path: 'user',
      model: 'ChatUser',
      select: '-chats'
    }).populate({
      path: 'chat',
      model: 'Chat',
      select: '-users -jobProUserIds'
    });
  }
  async find(
  page: number,
  pageSize: number,
  activityType: ACTIVITY_TYPE,
  user: ChatUserDto,
  tenantId: string
  ) {
    try {
      // Ensure safe defaults
      page = Number.isInteger(page) && page >= 0 ? page : 0;
      pageSize = Number.isInteger(pageSize) && pageSize > 0 ? pageSize : 100;

      // If page is greater than 0, minus 1
      page = page > 0 ? page - 1 : page;

      this.logService.log('page:', page, 'pageSize:', pageSize, 'activityType:', activityType);

      // Build query params
      const query: any = {
        user: user.id,
        tenantId
      };
      if (activityType) {
        query.activityType = activityType;
      }

      this.logService.log('Query Params:', page, pageSize);

      // Get paginated activities
      const paginatedActivity = await this.chatActivityModel
        .find(query)
        .sort({ isRead: 1, createdAt: -1 }) // combined sorting
        .limit(pageSize)
        .skip(page * pageSize)
        .populate({ path: 'message', model: 'ChatMessage' })
        .populate({ path: 'sender', model: 'ChatUser', select: '-chats' })
        .populate({ path: 'user', model: 'ChatUser', select: '-chats' })
        .populate({ path: 'chat', model: 'Chat', select: '-users -jobProUserIds' });

      // Get unread count
      const unReadCount = await this.chatActivityModel.countDocuments({
        isRead: false,
        user: user.id,
        tenantId
      });

      this.logService.log('Result:', { unReadCount, paginatedActivity });

      return { paginatedActivity, page, pageSize, unReadCount };
    } catch (error) {
      this.logService.log('Error getting paginated chat activity data:', error);
      throw error; // rethrow so caller can handle
    }
  }
}
