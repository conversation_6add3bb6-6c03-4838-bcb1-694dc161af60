/*
https://docs.nestjs.com/providers#services
*/

import { Injectable } from '@nestjs/common';
import { LogService } from 'src/log/log.service';
import { RabbitMQService } from 'src/rabbitmq/rabbitmq.service';
import { Applications, NotificationKey, NotificationMessageModel, NotificationType, RabbitMQQueues } from 'src/rabbitmq/dtos';
import { ChatuserService } from 'src/user/services/v2/chatuser.service';
import { ChatUser } from 'src/models/chat-user.model';
import { Socket } from 'socket.io';
import { CHAT_CALL_FORMAT, CHAT_CALL_STATUS, CHAT_CALL_TYPE, ChatCallDto } from 'src/messages/dto/chat-call.dto';
import { ChatService } from './chat.service';
import { CHAT_TYPE } from 'src/models/chat.model';

@Injectable()
export class CallNotificationService {
    constructor(
        private readonly logService: LogService,
        private readonly rabbitMQService: RabbitMQService,
        private readonly chatUserService: ChatuserService,
        private readonly chatService: ChatService
    ) {}

    /**
     * Extract auth token from socket handshake
     */
    private extractAuthToken(socket: Socket): string {
        let { authorization } = socket.handshake.auth;
        if (authorization?.includes(' ')) {
            const array = authorization?.split(' ');
            authorization = array.length > 1 ? array[1] : array[0];
        }
        return authorization;
    }

    /**
     * Send push notification for incoming call
     */
    async sendIncomingCallNotification(
        callData: ChatCallDto,
        caller: ChatUser,
        receiverIds: string[],
        socket: Socket,
        initiateCallPayload?: any
    ): Promise<void> {
        try {
            const authToken = this.extractAuthToken(socket);
            const chat = await this.chatService.findChatById(callData.meetingId || callData.chatId);
            
            for (const receiverId of receiverIds) {
                const receiver = await this.chatUserService.findUser(receiverId);
                if (!receiver || receiver.id === caller.id) continue;

                const callTypeText = callData.format === CHAT_CALL_FORMAT.VIDEO ? 'video' : 'audio';
                const chatName = chat?.name || (chat?.chatType === CHAT_TYPE.DM ? caller.jobProUserId || caller.email : 'Group Chat');
                
                let notificationBody: string;
                let notificationTitle: string;

                if (chat?.chatType === CHAT_TYPE.DM) {
                    notificationTitle = `Incoming ${callTypeText} call`;
                    notificationBody = `${caller.jobProUserId || caller.email} is calling you`;
                } else {
                    notificationTitle = `Incoming ${callTypeText} call`;
                    notificationBody = `${caller.jobProUserId || caller.email} is calling in ${chatName}`;
                }

                const notification = new NotificationMessageModel();
                notification.application = Applications.Joble;
                notification.body = notificationBody;
                notification.subject = notificationTitle;
                notification.userId = receiver.jobProUserId;
                notification.notificationTypes = [NotificationType.Push];
                notification.notificationKey = NotificationKey.Call
                // Push token would be handled by the external notification service

                // Add call-specific data for handling the notification

                var name = caller.email?.split('@')[0];
                name = name.charAt(0).toUpperCase() + name.slice(1);

                notification.templateWithPlaceHolders = {
                    template: 'incoming_call',
                    props: {
                        callId: callData.callId,
                        callerId: caller.id,
                        callerName: name || caller.jobProUserId || caller.email,
                        chatId: callData.meetingId || callData.chatId,
                        callType: callData.format,
                        chatType: chat?.chatType || CHAT_TYPE.DM,
                        key: 'incoming_call',
                        initiateCallPayload: JSON.stringify(initiateCallPayload) || null
                    }
                };

                this.logService.log('Sending incoming call notification: ', notification);
                await this.rabbitMQService.sendNotification(
                    RabbitMQQueues.NOTIFICATION_MESSAGE_QUEUE, 
                    JSON.stringify(notification), 
                    authToken, 
                    socket
                );
            }
        } catch (error) {
            this.logService.error('Error sending incoming call notification: ', error);
        }
    }

    /**
     * Send push notification for missed call
     */
    async sendMissedCallNotification(
        callData: ChatCallDto, 
        caller: ChatUser, 
        receiverIds: string[], 
        socket: Socket
    ): Promise<void> {
        try {
            const authToken = this.extractAuthToken(socket);
            const chat = await this.chatService.findChatById(callData.meetingId || callData.chatId);
            
            for (const receiverId of receiverIds) {
                const receiver = await this.chatUserService.findUser(receiverId);
                if (!receiver || receiver.id === caller.id) continue;

                const callTypeText = callData.format === CHAT_CALL_FORMAT.VIDEO ? 'video' : 'audio';
                const chatName = chat?.name || (chat?.chatType === CHAT_TYPE.DM ? caller.jobProUserId || caller.email : 'Group Chat');
                
                let notificationBody: string;
                let notificationTitle: string;

                if (chat?.chatType === CHAT_TYPE.DM) {
                    notificationTitle = `Missed ${callTypeText} call`;
                    notificationBody = `You missed a ${callTypeText} call from ${caller.jobProUserId || caller.email}`;
                } else {
                    notificationTitle = `Missed ${callTypeText} call`;
                    notificationBody = `You missed a ${callTypeText} call from ${caller.jobProUserId || caller.email} in ${chatName}`;
                }

                const notification = new NotificationMessageModel();
                notification.application = Applications.Joble;
                notification.body = notificationBody;
                notification.subject = notificationTitle;
                notification.userId = receiver.jobProUserId;
                notification.notificationTypes = [NotificationType.Push];
                // Push token would be handled by the external notification service

                notification.templateWithPlaceHolders = {
                    template: 'missed_call',
                    props: {
                        callId: callData.callId,
                        callerId: caller.id,
                        callerName: caller.jobProUserId || caller.email,
                        chatId: callData.meetingId || callData.chatId,
                        callType: callData.format,
                        chatType: chat?.chatType || CHAT_TYPE.DM,
                        key: 'missed_call'
                    }
                };

                this.logService.log('Sending missed call notification: ', notification);
                await this.rabbitMQService.sendNotification(
                    RabbitMQQueues.NOTIFICATION_MESSAGE_QUEUE, 
                    JSON.stringify(notification), 
                    authToken, 
                    socket
                );
            }
        } catch (error) {
            this.logService.error('Error sending missed call notification: ', error);
        }
    }

    /**
     * Send push notification for call ended
     */
    async sendCallEndedNotification(
        callData: ChatCallDto, 
        caller: ChatUser, 
        receiverIds: string[], 
        socket: Socket
    ): Promise<void> {
        try {
            const authToken = this.extractAuthToken(socket);
            const chat = await this.chatService.findChatById(callData.meetingId || callData.chatId);
            
            for (const receiverId of receiverIds) {
                const receiver = await this.chatUserService.findUser(receiverId);
                if (!receiver || receiver.id === caller.id) continue;

                const callTypeText = callData.format === CHAT_CALL_FORMAT.VIDEO ? 'video' : 'audio';
                const chatName = chat?.name || (chat?.chatType === CHAT_TYPE.DM ? caller.jobProUserId || caller.email : 'Group Chat');
                
                let notificationBody: string;
                let notificationTitle: string;

                if (chat?.chatType === CHAT_TYPE.DM) {
                    notificationTitle = `Call ended`;
                    notificationBody = `${callTypeText} call with ${caller.jobProUserId || caller.email} has ended`;
                } else {
                    notificationTitle = `Call ended`;
                    notificationBody = `${callTypeText} call in ${chatName} has ended`;
                }

                const notification = new NotificationMessageModel();
                notification.application = Applications.Joble;
                notification.body = notificationBody;
                notification.subject = notificationTitle;
                notification.userId = receiver.jobProUserId;
                notification.notificationTypes = [NotificationType.Push];
                // Push token would be handled by the external notification service

                notification.templateWithPlaceHolders = {
                    template: 'call_ended',
                    props: {
                        callId: callData.callId,
                        callerId: caller.id,
                        callerName: caller.jobProUserId || caller.email,
                        chatId: callData.meetingId || callData.chatId,
                        callType: callData.format,
                        key: 'call_ended',
                        chatType: chat?.chatType || CHAT_TYPE.DM
                    }
                };

                this.logService.log('Sending call ended notification: ', notification);
                await this.rabbitMQService.sendNotification(
                    RabbitMQQueues.NOTIFICATION_MESSAGE_QUEUE, 
                    JSON.stringify(notification), 
                    authToken, 
                    socket
                );
            }
        } catch (error) {
            this.logService.error('Error sending call ended notification: ', error);
        }
    }

    /**
     * Check if user is online/active in the chat
     */
    private async isUserOnline(userId: string, chatId: string, server: any): Promise<boolean> {
        try {
            // Check if user is connected to the specific chat room
            const sockets = await server.in(chatId).fetchSockets();
            return sockets.some(socket => socket.rooms.has(userId));
        } catch (error) {
            this.logService.error('Error checking user online status: ', error);
            return false;
        }
    }

    /**
     * Get offline users from a list of user IDs
     */
    async getOfflineUsers(userIds: string[], chatId: string, server: any): Promise<string[]> {
        const offlineUsers: string[] = [];

        console.log('userIds: ', userIds);
        for (const userId of userIds) {
            offlineUsers.push(userId)
            // const isOnline = await this.isUserOnline(userId, chatId, server);
            // if (!isOnline) {
            //     offlineUsers.push(userId);
            // }
        }

        console.log('offlineUsers: ', offlineUsers);
        return offlineUsers;
    }

    /**
     * Send push notification for call declined
     */
    async sendCallDeclinedNotification(
        callData: ChatCallDto,
        decliner: ChatUser,
        callerId: string,
        socket: Socket
    ): Promise<void> {
        try {
            const authToken = this.extractAuthToken(socket);
            const caller = await this.chatUserService.findUser(callerId);
            if (!caller || caller.id === decliner.id) return;

            const chat = await this.chatService.findChatById(callData.meetingId || callData.chatId);
            const callTypeText = callData.format === CHAT_CALL_FORMAT.VIDEO ? 'video' : 'audio';
            const chatName = chat?.name || (chat?.chatType === CHAT_TYPE.DM ? decliner.jobProUserId || decliner.email : 'Group Chat');

            let notificationBody: string;
            let notificationTitle: string;

            if (chat?.chatType === CHAT_TYPE.DM) {
                notificationTitle = `Call declined`;
                notificationBody = `${decliner.jobProUserId || decliner.email} declined your ${callTypeText} call`;
            } else {
                notificationTitle = `Call declined`;
                notificationBody = `${decliner.jobProUserId || decliner.email} declined the ${callTypeText} call in ${chatName}`;
            }

            const notification = new NotificationMessageModel();
            notification.application = Applications.Joble;
            notification.body = notificationBody;
            notification.subject = notificationTitle;
            notification.userId = caller.jobProUserId;
            notification.notificationTypes = [NotificationType.Push];
            // Push token would be handled by the external notification service

            notification.templateWithPlaceHolders = {
                template: 'call_declined',
                props: {
                    callId: callData.callId,
                    declinerId: decliner.id,
                    declinerName: decliner.jobProUserId || decliner.email,
                    chatId: callData.meetingId || callData.chatId,
                    callType: callData.format,
                    key: 'call_declined',
                    chatType: chat?.chatType || CHAT_TYPE.DM
                }
            };

            this.logService.log('Sending call declined notification: ', notification);
            await this.rabbitMQService.sendNotification(
                RabbitMQQueues.NOTIFICATION_MESSAGE_QUEUE,
                JSON.stringify(notification),
                authToken,
                socket
            );
        } catch (error) {
            this.logService.error('Error sending call declined notification: ', error);
        }
    }

    /**
     * Send push notification for call answered
     */
    async sendCallAnsweredNotification(
        callData: ChatCallDto,
        answerer: ChatUser,
        callerId: string,
        socket: Socket
    ): Promise<void> {
        try {
            const authToken = this.extractAuthToken(socket);
            const caller = await this.chatUserService.findUser(callerId);
            if (!caller || caller.id === answerer.id) return;

            const chat = await this.chatService.findChatById(callData.meetingId || callData.chatId);
            const callTypeText = callData.format === CHAT_CALL_FORMAT.VIDEO ? 'video' : 'audio';
            const chatName = chat?.name || (chat?.chatType === CHAT_TYPE.DM ? answerer.jobProUserId || answerer.email : 'Group Chat');

            let notificationBody: string;
            let notificationTitle: string;

            if (chat?.chatType === CHAT_TYPE.DM) {
                notificationTitle = `Call answered`;
                notificationBody = `${answerer.jobProUserId || answerer.email} answered your ${callTypeText} call`;
            } else {
                notificationTitle = `Call answered`;
                notificationBody = `${answerer.jobProUserId || answerer.email} joined the ${callTypeText} call in ${chatName}`;
            }

            const notification = new NotificationMessageModel();
            notification.application = Applications.Joble;
            notification.body = notificationBody;
            notification.subject = notificationTitle;
            notification.userId = caller.jobProUserId;
            notification.notificationTypes = [NotificationType.Push];
            // Push token would be handled by the external notification service

            notification.templateWithPlaceHolders = {
                template: 'call_answered',
                props: {
                    callId: callData.callId,
                    answererId: answerer.id,
                    answererName: answerer.jobProUserId || answerer.email,
                    chatId: callData.meetingId || callData.chatId,
                    callType: callData.format,
                    key: 'call_answered',
                    chatType: chat?.chatType || CHAT_TYPE.DM
                }
            };

            this.logService.log('Sending call answered notification: ', notification);
            await this.rabbitMQService.sendNotification(
                RabbitMQQueues.NOTIFICATION_MESSAGE_QUEUE,
                JSON.stringify(notification),
                authToken,
                socket
            );
        } catch (error) {
            this.logService.error('Error sending call answered notification: ', error);
        }
    }

    /**
     * Send push notification for call cancelled
     */
    async sendCallCancelledNotification(
        callData: ChatCallDto,
        caller: ChatUser,
        receiverIds: string[],
        socket: Socket
    ): Promise<void> {
        try {
            const authToken = this.extractAuthToken(socket);
            const chat = await this.chatService.findChatById(callData.meetingId || callData.chatId);

            for (const receiverId of receiverIds) {
                const receiver = await this.chatUserService.findUser(receiverId);
                if (!receiver || receiver.id === caller.id) continue;

                const callTypeText = callData.format === CHAT_CALL_FORMAT.VIDEO ? 'video' : 'audio';
                const chatName = chat?.name || (chat?.chatType === CHAT_TYPE.DM ? caller.jobProUserId || caller.email : 'Group Chat');

                let notificationBody: string;
                let notificationTitle: string;

                if (chat?.chatType === CHAT_TYPE.DM) {
                    notificationTitle = `Call cancelled`;
                    notificationBody = `${caller.jobProUserId || caller.email} cancelled the ${callTypeText} call`;
                } else {
                    notificationTitle = `Call cancelled`;
                    notificationBody = `${caller.jobProUserId || caller.email} cancelled the ${callTypeText} call in ${chatName}`;
                }

                const notification = new NotificationMessageModel();
                notification.application = Applications.Joble;
                notification.body = notificationBody;
                notification.subject = notificationTitle;
                notification.userId = receiver.jobProUserId;
                notification.notificationTypes = [NotificationType.Push];
                // Push token would be handled by the external notification service

                notification.templateWithPlaceHolders = {
                    template: 'call_cancelled',
                    props: {
                        callId: callData.callId,
                        callerId: caller.id,
                        callerName: caller.jobProUserId || caller.email,
                        chatId: callData.meetingId || callData.chatId,
                        callType: callData.format,
                        key: 'call_cancelled',
                        chatType: chat?.chatType || CHAT_TYPE.DM
                    }
                };

                this.logService.log('Sending call cancelled notification: ', notification);
                await this.rabbitMQService.sendNotification(
                    RabbitMQQueues.NOTIFICATION_MESSAGE_QUEUE,
                    JSON.stringify(notification),
                    authToken,
                    socket
                );
            }
        } catch (error) {
            this.logService.error('Error sending call cancelled notification: ', error);
        }
    }
}
