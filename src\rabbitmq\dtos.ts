import { v4 } from "uuid";
export class NotificationMessageModel {
    public messageId: string;

    // This is the message body for sms and push notification
    public body: string;

    // For templates with actual params
    public templateWithParams?: string;
    public subject?: string;
    public recipientEmails?: string[];
    public phoneNumber?: string;
    public pushToken?: string;
    public cc: string[] = [];
    public userId: string;
    public application: Applications;
    public attachments: Attachments[] = [];
    public notificationTypes: NotificationType[] = [];
    public notificationKey?: NotificationKey
    public priority: NotificationPriority;
    public isWhatsappNo: boolean;

    // For templates with params placeholders
    public templateWithPlaceHolders?: MessageTemplate;

    constructor() {
        this.messageId = v4();
        this.priority = NotificationPriority.High;
        this.isWhatsappNo = false;
    }
}

export enum NotificationPriority {
    High,
    Medium,
    Low
}

export enum NotificationType {
    Email = 1,
    Sms,
    Push
}

export enum NotificationKey {
    Chat = 1,
    Call
}

export class Attachments {
    public fileName: string;
    public attachment: Buffer; // Or Buffer, depending on your use case
}

export class MessageTemplate {
    public template: string;
    public props: { [key: string]: string }; // Equivalent to Dictionary<string, string>
    public additionalProps?: Record<string, any>;
}

export enum Applications {
    Joble = 1,
    JobPays,
    JobID,
    JobCRM,
    Echo,
    ProEvent,
    JobMatch,
    JobEMS
}

export enum RabbitMQQueues {
    WORKSPACE_CREATED_QUEUE = 'workspace-created-queue',
    NOTIFICATION_MESSAGE_QUEUE = 'notification-message-queue',
    USER_CREATED_QUEUE = 'user-created-queue'
}

export enum RabbitMQEvents {
    WORKSPACE_CREATED_EVENT = 'workspace-created-event',
    NOTIFICATION_MESSAGE_EVENT = 'notification-message-event',
    USER_CREATED_EVENT = 'user-created-event'
}

